<template>
  <div class="search-page">
    <header class="page-header">
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" @search="handleSearch">
        <template #right-action>
          <button class="layout-toggle" @click="toggleLayout" type="button" aria-label="切换布局">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>
      <SortFilterBar :sort-type="sortType" :sort-order="sortOrder" :has-filter-conditions="hasFilterConditions"
        @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <main class="page-content">
      <GoodsListLayout
        :goods-list="goodsList"
        :is-loading="isLoading"
        :loading="loading"
        :finished="finished"
        :is-waterfall-layout="isWaterfallLayout"
        :empty-description="'未搜到相关商品'"
        @load="onLoad"
        @item-click="goToDetail"
        @add-cart="addOneCart"
      />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart" />
    <FilterPopup v-model:show="isPopupShow" v-model="filterCriteria" :location-text="locationText"
      :category-id="categoryId" @switch-address="setSwitchAddressPopupShow" @confirm="handleFilterConfirm"
      @reset="handleFilterReset" />
    <AddressSwitchPopup v-model:show="isSwitchAddressPopupShow" @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import SearchHeader from '@components/Common/SearchHeader.vue'
import { useRoute, useRouter } from 'vue-router'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import { getBizCode } from '@utils/curEnv.js'
import { searchKeyWord, addOneClick } from '@/api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@/store/modules/user.js'
import { useNewCartStore } from '@/store/modules/newCart.js'
import AddressSwitchPopup from '@/components/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@/components/FilterTools/FilterPopup.vue'
import FloatingBubble from '@/components/FilterTools/FloatingBubble.vue'
import SortFilterBar from '@/components/FilterTools/SortFilterBar.vue'
import { get, debounce, pick, compact, isEmpty } from 'lodash-es'

const userStore = useUserStore()
const cartStore = useNewCartStore()
const router = useRouter()
const route = useRoute()

// 基础状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('desc')
const goodsList = ref([])
const loading = ref(false)
const finished = ref(false)
const isLoading = ref(true)
const isWaterfallLayout = ref(false)
const pageNo = ref(1)
const pageSize = ref(10)
const categoryId = ref('')

// 弹窗状态
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 筛选条件
const filterCriteria = ref({
  isStock: false,
  minPrice: '',
  maxPrice: '',
  brandsList: []
})

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// 计算属性
const hasFilterConditions = computed(() => {
  const { isStock, minPrice, maxPrice, brandsList } = filterCriteria.value
  return isStock ||
    !isEmpty(minPrice) ||
    !isEmpty(maxPrice) ||
    brandsList.some(brand => brand.isSelected)
})

const curAddrInfo = computed(() => userStore.curAddressInfo)

const locationText = computed(() => get(curAddrInfo.value, 'addrDetail', ''))

const addressInfo = computed(() => {
  const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
  return JSON.stringify(pick(curAddrInfo.value, addressFields))
})

// 方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  pageNo.value = 1
  finished.value = false
  goodsList.value = []
  fetchGoodsList()
}, 300)

const handleSearch = () => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: searchKeyword.value
    }
  })
  debouncedSearch()
}



const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  pageNo.value = 1
  finished.value = false
  goodsList.value = []
  fetchGoodsList()
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

const handleFilterConfirm = (criteria) => {
  console.log('应用筛选条件:', criteria)
  pageNo.value = 1
  finished.value = false
  goodsList.value = []
  fetchGoodsList()
}

const handleFilterReset = () => {
  console.log('重置筛选条件')
}

const handleAddressChanged = () => {
  pageNo.value = 1
  finished.value = false
  goodsList.value = []
  fetchGoodsList()
}

const consecutiveEmptyPages = ref(0) // 连续空页面计数
const maxEmptyPages = 2 // 最大连续空页面数

// 获取商品列表
const fetchGoodsList = async () => {
  showLoadingToast()

  // 先获取最新的地址信息，确保地址是最新的
  if (pageNo.value === 1) {
    await userStore.queryDefaultAddr({ force: true })
    isLoading.value = true
    goodsList.value = []
    consecutiveEmptyPages.value = 0 // 重置连续空页面计数
    finished.value = false // 重置完成状态
  }

  const brandList = compact(
    filterCriteria.value.brandsList
      .filter(item => item.isSelected)
      .map(item => item.value)
  )

  const testDMX = get(route.query, 'testDMX', 'false')
  const [err, json] = await searchKeyWord({
    keyword: searchKeyword.value,
    bizCode: getBizCode('GOODS'),
    pageNumber: pageNo.value,
    pageSize: pageSize.value,
    orderType: '00',
    orderRule: sortOrder.value,
    brands: brandList,
    minPrice: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice) : '',
    maxPrice: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice) : '',
    addressJsonInfo: addressInfo.value,
    testDMX
  })

  closeToast()
  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.length > 0) {
      const tmpList = json || []
      tmpList.forEach((item) => {
        // 使用
        item.showImageUrl = get(item, 'imageUrl', '').split(',')[0]

        // 使用 compact 过滤空值参数
        item.params = compact([
          item.param,
          item.param1,
          item.param2,
          item.param3,
          item.param4
        ])

        item.realSaleVolume = item.sales
      })

      // 库存筛选处理
      let filteredList = tmpList
      if (filterCriteria.value.isStock) {
        filteredList = tmpList.filter(item => item.stock > 0)
      }

      if (filteredList.length > 0) {
        // 有有效数据，重置连续空页面计数
        consecutiveEmptyPages.value = 0
        goodsList.value = goodsList.value.concat(filteredList)
        pageNo.value++
      } else {
        // 筛选后无数据，但原始数据有内容，继续请求下一页
        consecutiveEmptyPages.value++
        if (consecutiveEmptyPages.value >= maxEmptyPages) {
          finished.value = true
          return
        }
        pageNo.value++
        // 继续加载下一页
        nextTick(() => {
          onLoad()
        })
      }
    } else {
      // 当前页无原始数据
      consecutiveEmptyPages.value++

      if (consecutiveEmptyPages.value >= maxEmptyPages) {
        // 连续两页都没有数据，结束加载
        finished.value = true
        return
      }

      // 继续请求下一页
      pageNo.value++
      nextTick(() => {
        onLoad()
      })
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
    showToast({
      message: err.msg,
    })
    // 请求失败也算作空页面
    consecutiveEmptyPages.value++
    if (consecutiveEmptyPages.value >= maxEmptyPages) {
      finished.value = true
    }
  }
}

// 滚动加载处理
const onLoad = () => {
  if (!finished.value) {
    fetchGoodsList()
  }
}

const goToDetail = (item) => {
  console.log('跳转到商品详情', item)
  router.push(`/goodsdetail/${item.goodsId}/${item.skuId}`)
}

const goToCart = () => {
  // console.log('跳转到购物车')
  router.push('/cart')
}

// 一键加入购物车
const addOneCart = async (item) => {
  try {
    showLoadingToast()

    const [err] = await addOneClick({
      bizCode: getBizCode('ORDER'),
      skuId: item.skuId,
      goodsId: item.goodsId,
      addressInfo: addressInfo.value
    })

    closeToast()

    if (err) {
      showToast(err.msg)
      return
    }

    showToast('加入购物车成功！')
    await cartStore.query()
  } catch (error) {
    closeToast()
    console.error('加入购物车失败:', error)
    showToast('加入购物车失败，请重试')
  }
}

// 组件挂载
onMounted(async () => {
  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: 85px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 17px;

    .skeleton-container {
      margin-top: 10px;

      .skeleton-block {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
        width: 100%;
        height: 100%;
      }

      @keyframes skeleton-loading {
        0% {
          background-position: 200% 0;
        }

        100% {
          background-position: -200% 0;
        }
      }

      // 列表布局骨架屏样式
      .list-skeleton {
        .skeleton-item {
          display: flex;
          background-color: #fff;
          border-radius: 8px;
          overflow: hidden;
          margin-bottom: 12px;
          cursor: pointer;

          .skeleton-image {
            width: 85px;
            height: 85px;
            flex-shrink: 0;
          }

          .skeleton-info {
            flex: 1;
            margin-left: 8px;
            padding-top: 8px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .skeleton-info-main {
              .skeleton-title {
                height: 13px;
                margin-bottom: 6px;
                border-radius: 4px;
                width: 80%;
              }

              .skeleton-spec {
                height: 11px;
                margin: 4px 0 8px 0;
                border-radius: 4px;
                width: 60%;
              }
            }

            .skeleton-info-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-bottom: 8px;

              .skeleton-price {
                height: 12px;
                border-radius: 4px;
                width: 40%;
              }

              .skeleton-cart {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }

      // 瀑布流布局骨架屏样式
      .waterfall-skeleton {
        .skeleton-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
        }

        .skeleton-item {
          background-color: #fff;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;

          .skeleton-image {
            width: 100%;
            height: 120px;
            border-radius: 8px 8px 0 0;
          }

          .skeleton-content {
            padding: 10px;

            .skeleton-title {
              height: 13px;
              margin-bottom: 6px;
              border-radius: 4px;
              width: 90%;
            }

            .skeleton-spec {
              height: 11px;
              margin: 4px 0;
              border-radius: 4px;
              width: 70%;
            }

            .skeleton-sales {
              height: 11px;
              margin: 4px 0;
              border-radius: 4px;
              width: 50%;
            }

            .skeleton-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 8px;

              .skeleton-price {
                height: 12px;
                border-radius: 4px;
                width: 50%;
              }

              .skeleton-cart {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }

      // 骨架屏通用样式
      .skeleton-title,
      .skeleton-spec,
      .skeleton-sales,
      .skeleton-price {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
      }
    }

    // 列表布局样式
    .list-layout {
      margin-top: 2px;
      .goods-list-container {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .goods-item {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 12px;
        cursor: pointer;

        .goods-image {
          width: 85px;
          height: 85px;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .goods-info {
          flex: 1;
          margin-left: 8px;
          padding-top: 8px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .goods-info-main {
            .goods-title {
              font-size: 13px;
              color: #333;
              margin: 0;
              font-weight: normal;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }

            .goods-spec {
              font-size: 11px;
              color: #666;
              line-height: 1.5;
              margin: 4px 0 0 0;
            }
          }

          .goods-info-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;

            .price-sales {
              display: flex;
              align-items: center;

              .sales-count {
                font-size: 11px;
                color: #999;
                margin-left: 12px;
              }
            }

            .cart-btn {
              background: none;
              border: none;
              padding: 0;
              cursor: pointer;

              img {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }
    }

    // 瀑布流布局样式
    .waterfall-layout {
      .waterfall-item {
        break-inside: avoid;
        cursor: pointer;

        .waterfall-card {
          background-color: #fff;
          overflow: hidden;

          .waterfall-image {
            width: 100%;
            overflow: hidden;
            position: relative;
            border-radius: 8px;

            img {
              width: 100%;
              height: auto;
              object-fit: cover;
              border-radius: 8px;
            }
          }

          .waterfall-content {
            margin-top: 10px;

            .waterfall-title {
              font-size: 13px;
              color: #333;
              margin: 0;
              font-weight: normal;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }

            .waterfall-spec {
              font-size: 11px;
              color: #666;
              line-height: 1.5;
              margin: 4px 0 0 0;
            }

            .waterfall-sales {
              font-size: 11px;
              color: #999;
              margin: 4px 0 0 0;
            }

            .waterfall-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 8px;

              .cart-btn {
                background: none;
                border: none;
                padding: 0;
                cursor: pointer;

                img {
                  width: 25px;
                  height: 25px;
                }
              }
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px 0;
    }
  }
}
</style>
